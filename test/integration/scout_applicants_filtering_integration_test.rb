require 'test_helper'

class ScoutApplicantsFilteringIntegrationTest < ActionDispatch::IntegrationTest
  def setup
    # Clean up any existing data
    JobApplication.destroy_all
    Job.destroy_all
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all

    # Create scout user
    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Scout',
        last_name: 'Test',
        scout_signup_completed: true,
        verified: true,
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    # Create organization and associate scout
    @organization = Organization.create!(name: "Scout's Test Org")
    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )
    @scout.update!(last_logged_in_organization_id: @organization.id)

    # Create jobs
    @job1 =
      Job.create!(
        title: 'Test Job 1',
        description: 'Test job description 1',
        job_category: 'social_media',
        platform: 'linkedin',
        outcome: 'leads',
        social_media_goal_type: 'social_media_leads',
        social_media_understands_risk_acknowledged: '1',
        budget_range: 'range_1000_2000',
        work_duration: 'one_time_project',
        status: 'published',
        organization: @organization,
      )

    @job2 =
      Job.create!(
        title: 'Test Job 2',
        description: 'Test job description 2',
        job_category: 'newsletter',
        outcome: 'grow_email_list',
        newsletter_frequency: 'weekly',
        newsletter_length: 'words_300_600',
        budget_range: 'range_1000_2000',
        work_duration: 'long_term',
        status: 'published',
        organization: @organization,
      )

    # Sign in as scout
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }
  end

  def teardown
    JobApplication.destroy_all
    Job.destroy_all
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  def create_talent_and_application(name_suffix, status, job = @job1)
    talent =
      User.create!(
        email: "talent_#{name_suffix}@example.com",
        password: 'password123456',
        first_name: 'Talent',
        last_name: name_suffix.titleize,
        talent_signup_completed: true,
        verified: true,
      )

    JobApplication.create!(
      user: talent,
      job: job,
      application_letter: "Test application letter for #{name_suffix}",
      status: status,
      applied_at: rand(1..30).days.ago,
    )

    talent
  end

  test 'handles empty state when no applications exist for a status' do
    # Create only reviewed applications
    create_talent_and_application('reviewed_one', 'reviewed')
    create_talent_and_application('reviewed_two', 'reviewed')

    # Test filtering by qualified (which has no applications)
    get scout_applicants_path, params: { status: 'qualified' }

    assert_response :success

    # Should not show any applications
    assert_not_includes response.body, 'Talent Reviewed_one'
    assert_not_includes response.body, 'Talent Reviewed_two'

    # Should still show the status buttons with correct counts
    assert_includes response.body, '2 Total'
    assert_includes response.body, '2 Reviewed'
    assert_includes response.body, '0' # Qualified count should be 0
  end

  test 'handles large number of applications efficiently' do
    # Create multiple applications for each status
    5.times { |i| create_talent_and_application("reviewed_#{i}", 'reviewed') }
    3.times { |i| create_talent_and_application("qualified_#{i}", 'qualified') }
    2.times { |i| create_talent_and_application("accepted_#{i}", 'accepted') }

    # Test unfiltered view
    get scout_applicants_path
    assert_response :success
    assert_includes response.body, '10 Total'
    assert_includes response.body, '5 Reviewed'
    assert_includes response.body, '3 Qualified'
    assert_includes response.body, '2 Accepted'

    # Test each filter
    get scout_applicants_path, params: { status: 'reviewed' }
    assert_response :success

    # Should show all 5 reviewed applications
    (0..4).each { |i| assert_includes response.body, "Talent Reviewed_#{i}" }

    # Should not show qualified or accepted
    (0..2).each do |i|
      assert_not_includes response.body, "Talent Qualified_#{i}"
    end
  end

  test 'status filtering works with job filtering' do
    # Create applications for different jobs and statuses
    talent1 = create_talent_and_application('job1_reviewed', 'reviewed', @job1)
    talent2 =
      create_talent_and_application('job1_qualified', 'qualified', @job1)
    talent3 = create_talent_and_application('job2_reviewed', 'reviewed', @job2)
    talent4 = create_talent_and_application('job2_accepted', 'accepted', @job2)

    # Test filtering by status and job
    get scout_applicants_path, params: { status: 'reviewed', job_id: @job1.id }
    assert_response :success

    # Should show only reviewed applications for job1
    assert_includes response.body, talent1.name.full
    assert_not_includes response.body, talent2.name.full # different status
    assert_not_includes response.body, talent3.name.full # different job
    assert_not_includes response.body, talent4.name.full # different job and status
  end

  test 'status filtering preserves other URL parameters' do
    create_talent_and_application('reviewed_one', 'reviewed')
    create_talent_and_application('qualified_one', 'qualified')

    # Test with additional parameters
    get scout_applicants_path,
        params: {
          status: 'reviewed',
          job_id: @job1.id,
          page: 2,
          custom_param: 'test_value',
        }

    assert_response :success

    # Should preserve the status filter
    assert_includes response.body, 'Talent Reviewed_one'
    assert_not_includes response.body, 'Talent Qualified_one'
  end

  test 'invalid status parameter is handled gracefully' do
    create_talent_and_application('reviewed_one', 'reviewed')
    create_talent_and_application('qualified_one', 'qualified')

    # Test with invalid status
    get scout_applicants_path, params: { status: 'invalid_status' }

    assert_response :success

    # Should show no applications for invalid status
    assert_not_includes response.body, 'Talent Reviewed_one'
    assert_not_includes response.body, 'Talent Qualified_one'

    # Should still show status buttons
    assert_includes response.body, 'Application Stage'
    assert_includes response.body, 'Total'
  end

  test 'status button URLs are correct for different filter states' do
    create_talent_and_application('reviewed_one', 'reviewed')
    create_talent_and_application('qualified_one', 'qualified')
    create_talent_and_application('accepted_one', 'accepted')

    # Test unfiltered state - all status buttons should link to their filters
    get scout_applicants_path
    assert_response :success

    assert_select 'a[href=?]', scout_applicants_path, text: /Total/
    assert_select 'a[href=?]',
                  scout_applicants_path(status: 'reviewed'),
                  text: /Reviewed/
    assert_select 'a[href=?]',
                  scout_applicants_path(status: 'qualified'),
                  text: /Qualified/
    assert_select 'a[href=?]',
                  scout_applicants_path(status: 'accepted'),
                  text: /Accepted/

    # Test filtered state - selected button should clear filter
    get scout_applicants_path, params: { status: 'reviewed' }
    assert_response :success

    assert_select 'a[href=?]', scout_applicants_path, text: /Total/
    assert_select 'a[href=?]', scout_applicants_path, text: /Reviewed/ # Selected, so clears filter
    assert_select 'a[href=?]',
                  scout_applicants_path(status: 'qualified'),
                  text: /Qualified/
    assert_select 'a[href=?]',
                  scout_applicants_path(status: 'accepted'),
                  text: /Accepted/
  end

  test 'status counts are accurate across different scenarios' do
    # Create applications with various statuses
    2.times { create_talent_and_application('reviewed', 'reviewed') }
    1.times { create_talent_and_application('qualified', 'qualified') }
    3.times { create_talent_and_application('accepted', 'accepted') }
    1.times { create_talent_and_application('offered', 'offered') }

    get scout_applicants_path
    assert_response :success

    # Check that counts are displayed correctly
    assert_includes response.body, '7 Total'
    assert_includes response.body, '2 Reviewed'
    assert_includes response.body, '1 Qualified'
    assert_includes response.body, '3 Accepted'
    assert_includes response.body, '1 Offered'
  end

  test 'filtering works correctly with withdrawn applications' do
    # Create applications including withdrawn ones
    create_talent_and_application('reviewed_one', 'reviewed')
    create_talent_and_application('withdrawn_one', 'withdrawn')

    # Withdrawn applications should not appear in normal filtering
    get scout_applicants_path
    assert_response :success
    assert_includes response.body, 'Talent Reviewed_one'

    # Withdrawn applications might not be shown in the main view
    # This depends on the business logic of the application

    # Test filtering by reviewed status
    get scout_applicants_path, params: { status: 'reviewed' }
    assert_response :success
    assert_includes response.body, 'Talent Reviewed_one'
  end

  test 'status filtering respects organization boundaries' do
    # Create another organization and scout
    other_org = Organization.create!(name: 'Other Organization')
    other_scout =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Other',
        last_name: 'Scout',
        scout_signup_completed: true,
        verified: true,
      )
    OrganizationMembership.create!(
      user: other_scout,
      organization: other_org,
      org_role: 'admin',
    )

    # Create job for other organization
    other_job =
      Job.create!(
        title: 'Other Org Job',
        description: 'Job for other organization',
        job_category: 'social_media',
        platform: 'linkedin',
        outcome: 'leads',
        social_media_goal_type: 'social_media_leads',
        social_media_understands_risk_acknowledged: '1',
        budget_range: 'range_1000_2000',
        work_duration: 'one_time_project',
        status: 'published',
        organization: other_org,
      )

    # Create applications for both organizations
    create_talent_and_application('my_org_reviewed', 'reviewed', @job1)

    other_talent =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Other',
        last_name: 'Talent',
        talent_signup_completed: true,
        verified: true,
      )

    JobApplication.create!(
      user: other_talent,
      job: other_job,
      application_letter: 'Application for other org',
      status: 'reviewed',
      applied_at: 1.day.ago,
    )

    # Current scout should only see applications for their organization
    get scout_applicants_path, params: { status: 'reviewed' }
    assert_response :success

    assert_includes response.body, 'Talent My_org_reviewed'
    assert_not_includes response.body, 'Other Talent'
  end
end
