<div class="overflow-hidden bg-white border rounded-md shadow hover:shadow-md border-stone-300">
  <div class="flex">
    <div class="flex-1">
      <!-- Header Section -->
      <div class="p-5 border-b border-stone-200">
        <div class="flex items-start gap-4">
          <!-- Profile Image -->
          <% if talent_profile.user.avatar.attached? %>
            <%= image_tag talent_profile.user.avatar, class: "w-16 h-16 rounded-full object-cover border border-stone-200 flex-shrink-0" %>
          <% else %>
            <div class="flex items-center justify-center flex-shrink-0 w-16 h-16 text-2xl font-bold border rounded-full text-stone-700 bg-stone-100 border-stone-200">
              <%= talent_profile.user.name.first[0].upcase %>
            </div>
          <% end %>
          
          <!-- Basic Info -->
          <div class="flex-1">
            <div class="flex items-start justify-between">
              <div>
                <div class="flex items-center gap-2">
                  <h2 class="text-xl font-bold text-stone-800"><%= talent_profile.user.name.full %></h2>
                  <span class="px-2 py-0.5 text-xs font-medium rounded-full <%= talent_profile.available? ? 'bg-green-100 text-green-700' : (talent_profile.limited? ? 'bg-yellow-100 text-yellow-700' : 'bg-red-100 text-red-700') %>">
                    <%= talent_profile.availability_status.humanize %>
                  </span>
                </div>
                <p class="font-medium text-stone-600"><%= talent_profile.headline %></p>
              </div>

              <!-- Badges positioned to the left of bookmark button -->
              <div class="flex items-center gap-3">
                <%# Badge display in profile card %>
                <div class="mr-2">
                  <%= render 'shared/user_badges',
                      user: talent_profile.user,
                      context: 'card',
                      limit: 2 %>
                </div>

                <!-- Bookmark button -->
                <div id="bookmark_button_<%= talent_profile.id %>">
                  <%= button_to bookmark_scout_talent_path(talent_profile),
                      method: talent_profile.bookmarked_by?(Current.user) ? :delete : :post,
                      class: "p-2 rounded-md border border-stone-200 hover:border-stone-300 #{talent_profile.bookmarked_by?(Current.user) ? 'text-blue-600 hover:text-blue-700' : 'text-stone-400 hover:text-stone-600'}",
                      data: { turbo_method: talent_profile.bookmarked_by?(Current.user) ? :delete : :post } do %>
                    <%= phosphor_icon "bookmark-simple", class: "h-5 w-5", style: (talent_profile.bookmarked_by?(Current.user) ? 'fill' : 'default') %>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Main Content -->
      <div class="px-5 py-3 overflow-y-auto">
        <!-- Looking For Section -->
        <% if talent_profile.looking_for.present? %>
        <div class="mb-4">
          <h3 class="mb-2 text-sm font-semibold text-stone-700">
            Looking for
          </h3>
          <div class="pl-4 text-stone-700 border-stone-100">
            <p><%= talent_profile.looking_for %></p>
          </div>
        </div>
        <% end %>
        
        <!-- Ghostwriter Type -->
        <% if talent_profile.ghostwriter_type.present? && talent_profile.ghostwriter_type.reject(&:blank?).any? %>
        <div class="mb-4">
          <h3 class="mb-2 text-sm font-semibold text-stone-700">
            Ghostwriter Type
          </h3>
          
          <div class="flex flex-wrap gap-2 pl-4">
            <% talent_profile.ghostwriter_type.reject(&:blank?).each do |type| %>
              <span class="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium bg-stone-100 text-stone-800">
                <%= phosphor_icon "tag", class: "h-4 w-4 mr-1" %>
                <%= type %>
              </span>
            <% end %>
          </div>
        </div>
        <% end %>
        
        <!-- Content Topics -->
        <% if talent_profile.niches.present? && talent_profile.niches.any? %>
        <div class="mb-4">
          <h3 class="mb-2 text-sm font-semibold text-stone-700">
            Content Topics
          </h3>
          
          <div class="flex flex-wrap gap-2 pl-4">
            <% talent_profile.niches.each do |niche| %>
              <span class="px-3 py-1.5 bg-stone-100 rounded-md text-sm text-stone-700"><%= niche %></span>
            <% end %>
          </div>
        </div>
        <% end %>
        
        <!-- Social Media Speciality -->
        <% if talent_profile.social_media_specialty.present? && talent_profile.social_media_specialty.reject(&:blank?).any? %>
        <div class="mb-5">
          <h3 class="mb-2 text-sm font-semibold text-stone-700">
            Social Media Speciality
          </h3>
          
          <div class="flex flex-wrap gap-2 pl-4">
            <% talent_profile.social_media_specialty.reject(&:blank?).each do |platform| %>
              <% 
                platform_classes = case platform.downcase
                when "linkedin"
                  "bg-blue-100 text-blue-800"
                when "instagram"
                  "bg-pink-100 text-pink-800"
                when "twitter", "x"
                  "bg-blue-100 text-blue-800"
                when "threads"
                  "bg-stone-100 text-stone-800"
                when "facebook"
                  "bg-blue-100 text-blue-800"
                when "tiktok"
                  "bg-black bg-opacity-10 text-stone-800"
                else
                  "bg-stone-100 text-stone-800"
                end

                platform_icon = case platform.downcase
                when "linkedin"
                  phosphor_icon("linkedin-logo", class: "h-4 w-4")
                when "instagram"
                  phosphor_icon("instagram-logo", class: "h-4 w-4")
                when "twitter", "x"
                  phosphor_icon("twitter-logo", class: "h-4 w-4")
                when "threads"
                  phosphor_icon("threads-logo", class: "h-4 w-4")
                when "facebook"
                  phosphor_icon("facebook-logo", class: "h-4 w-4")
                when "tiktok"
                  phosphor_icon("tiktok-logo", class: "h-4 w-4")
                else
                  phosphor_icon("globe", class: "h-4 w-4")
                end
              %>
              <span class="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium <%= platform_classes %>">
                <span class="mr-1"><%= platform_icon %></span>
                <%= platform %>
              </span>
            <% end %>
          </div>
        </div>
        <% end %>
      </div>
      
      <!-- Footer with Action Buttons -->
      <div class="flex items-center justify-between p-4 border-t border-stone-200 bg-stone-50">
        <div></div>
        
        <div class="flex gap-3 text-sm">
          <%= link_to scout_talent_path(talent_profile), class: "flex items-center gap-1 px-2 py-1 rounded-md text-stone-900 hover:text-stone-800" do %>
            <span>View Profile</span>
          <% end %>
          <% if Current.user.has_pending_chat_request_with?(talent_profile.user) %>
            <button disabled class="px-6 py-2.5 bg-stone-400 text-stone-50 rounded-md cursor-not-allowed flex items-center gap-1">
              <span>Request Sent</span>
              <%= phosphor_icon("check", class: "h-4 w-4") %>
            </button>
          <% else %>
            <button type="button"
                    data-action="click->chat-request-button#openModal"
                    data-talent-user-id="<%= talent_profile.user.id %>"
                    class="px-6 py-2.5 bg-stone-900 text-stone-50 rounded-md hover:bg-stone-800 flex items-center gap-1">
              <span>Request to chat</span>
              <%= phosphor_icon("arrow-right", class: "h-4 w-4") %>
            </button>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
